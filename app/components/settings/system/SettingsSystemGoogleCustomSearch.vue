<template>
  <div>
    <SettingsSystemLoadings v-if="loadings['getAllSettings']" />
    <div
      v-else
      class="space-y-6"
    >
      <!-- Header with Add Button -->
      <!-- <div class="flex justify-between items-center">
        <h3 class="text-lg font-semibold">
          Google Custom Search 設定
        </h3>
        <UButton
          icon="material-symbols:add"
          color="primary"
          variant="solid"
          @click="onAddNewSetting"
        >
          新しい設定を追加
        </UButton>
      </div> -->

      <!-- Settings List -->
      <div
        v-if="settings.length === 0"
        class="text-center py-8 text-gray-500"
      >
        設定がありません。新しい設定を追加してください。
      </div>

      <div
        v-else
        class="space-y-6"
      >
        <div
          v-for="(setting, index) in settings"
          :key="setting.id || `new-${index}`"
          class=""
        >
          <!-- Setting Header -->
          <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-2">
              <h4 class="font-medium">
                {{ setting.category || `設定 ${index + 1}` }}
              </h4>
              <UBadge
                :color="setting.enabled ? 'green' : 'gray'"
                variant="subtle"
              >
                {{ setting.enabled ? "有効" : "無効" }}
              </UBadge>
            </div>
            <div class="flex gap-2">
              <UButton
                v-if="setting.id && !setting.isEditing"
                icon="material-symbols:delete"
                color="red"
                variant="ghost"
                size="sm"
                @click="onDeleteSetting(setting.id, index)"
              >
                削除
              </UButton>
            </div>
          </div>

          <!-- Setting Form (Edit Mode) -->
          <UForm
            :schema="schema"
            :state="setting"
            class="space-y-4"
            @submit="(event) => onSubmitSetting(event, index)"
          >
            <div class="flex flex-row gap-4 w-full">
              <div class="w-full flex flex-col gap-6 py-2">
                <UFormGroup
                  v-for="field in editableSettingFields"
                  :key="field.key"
                  :label="field.label"
                  :name="field.key"
                  :required="field.required"
                >
                  <BasePasswordInput
                    v-if="field.type === 'password'"
                    v-model="setting[field.key]"
                  />
                  <USelect
                    v-else-if="field.type === 'select'"
                    v-model="setting[field.key]"
                    :options="field.options"
                    value-attribute="value"
                    option-attribute="label"
                  />
                  <UToggle
                    v-else-if="field.type === 'boolean'"
                    v-model="setting[field.key]"
                  />
                  <URadioGroup
                    v-else-if="field.type === 'radio'"
                    v-model="setting[field.key]"
                    :options="field.options"
                    value-attribute="value"
                    option-attribute="label"
                  />
                  <UInput
                    v-else-if="field.type === 'number'"
                    v-model.number="setting[field.key]"
                    type="number"
                    :min="field.min"
                    :max="field.max"
                  />
                  <UTextarea
                    v-else-if="field.type === 'textarea'"
                    v-model="setting[field.key]"
                    :rows="3"
                  />
                  <BaseJsonPropertyEditor
                    v-else-if="field.type === 'json'"
                    v-model="setting[field.key]"
                  />
                  <UInput
                    v-else
                    v-model="setting[field.key]"
                    :disabled="field.readonly"
                    :color="field.readonly ? 'gray' : 'white'"
                  />
                </UFormGroup>
                <UAlert
                  v-if="errors[`setting-${index}`]"
                  icon="material-symbols:error"
                  color="red"
                  variant="subtle"
                  title="エラー"
                  :description="errors[`setting-${index}`]"
                />
                <BasicFormButtonGroup
                  :cancel-label="'キャンセル'"
                  :submit-label="setting.id ? '更新' : '作成'"
                  :loading="loadings[`setting-${index}`]"
                  @close="onCancelEdit(index)"
                />
              </div>
              <div
                v-if="setting.id"
                class="w-[45%] rounded-xl flex flex-col gap-4 bg-gray-50 dark:bg-gray-700 px-4 -mt-2 py-4 border border-gray-300 dark:border-gray-800"
              >
                <UFormGroup
                  v-for="field in readonlySettingFields"
                  :key="field.key"
                  :label="field.label"
                  :name="field.key"
                  :required="field.required"
                >
                  <BasePasswordInput
                    v-if="field.type === 'password'"
                    v-model="setting[field.key]"
                  />
                  <UInput
                    v-else
                    v-model="setting[field.key]"
                    :disabled="field.readonly"
                    :color="field.readonly ? 'gray' : 'white'"
                  />
                </UFormGroup>
              </div>
            </div>
          </UForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

const infraGoogleCustomSearchStore = useInfraGoogleCustomSearchStore()
const {
  settings,
  loadings,
  errors,
  editableSettingFields,
  readonlySettingFields
} = storeToRefs(infraGoogleCustomSearchStore)
const { selectedTenantId, selectedEnvId } = useApp()
const confirm = useConfirm()
const toast = useToast()

const schema = z.object({
  category: z
    .string({
      required_error: 'このフィールドは必須です。'
    })
    .trim()
    .min(1, 'このフィールドは必須です。')
    .max(100, '100文字以下で入力してください。'),
  description: z.string().optional(),
  api_key: z
    .string({
      required_error: 'このフィールドは必須です。'
    })
    .trim()
    .min(1, 'このフィールドは必須です。'),
  cse_id: z
    .string({
      required_error: 'このフィールドは必須です。'
    })
    .trim()
    .min(1, 'このフィールドは必須です。'),
  language: z.string({
    required_error: 'このフィールドは必須です。'
  }),
  safe_search: z.boolean().optional(),
  deep_scrapping: z.number().min(0).max(2).optional(),
  use_limit: z.number().min(0).max(1).optional(),
  enabled: z.boolean().optional(),
  extra_settings: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (!val || val.trim() === '') return true
        try {
          JSON.parse(val)
          return true
        } catch {
          return false
        }
      },
      {
        message: '有効なJSON形式で入力してください。'
      }
    )
})

type Schema = z.output<typeof schema>

onMounted(() => {
  infraGoogleCustomSearchStore.getAllSettings(
    selectedTenantId.value,
    selectedEnvId.value
  )
})

function onCancelEdit(index: number) {
  const setting = settings.value[index]
  if (setting.id) {
    // Existing setting - restore original values
    const original = infraGoogleCustomSearchStore.originalSettings.find(
      s => s.id === setting.id
    )
    if (original) {
      Object.assign(setting, { ...original, isEditing: false })
    }
  } else {
    // New setting - remove from list
    settings.value.splice(index, 1)
  }
}

async function onSubmitSetting(event: FormSubmitEvent<Schema>, index: number) {
  const setting = settings.value[index]
  const loadingKey = `setting-${index}`

  confirm.show({
    title: setting.id ? '設定更新の確認' : '設定作成の確認',
    description: setting.id
      ? '設定を更新しますか？'
      : '新しい設定を作成しますか？',
    confirmText: setting.id ? '更新' : '作成',
    onConfirm: async () => {
      try {
        loadings.value[loadingKey] = true
        errors.value[loadingKey] = null

        let result
        if (setting.id) {
          // Update existing setting
          result = await infraGoogleCustomSearchStore.updateSetting(
            setting.id,
            selectedTenantId.value,
            selectedEnvId.value,
            event.data
          )
        } else {
          // Create new setting
          result = await infraGoogleCustomSearchStore.createSetting(
            selectedTenantId.value,
            selectedEnvId.value,
            event.data
          )
        }

        if (result) {
          setting.isEditing = false
          if (!setting.id) {
            // Update the setting with the returned data
            Object.assign(setting, result)
          }
          toast.add({
            id: 'setting-update',
            title: setting.id ? '設定更新' : '設定作成',
            description: setting.id
              ? '設定を更新しました。'
              : '設定を作成しました。',
            color: 'green'
          })
        } else {
          // Handle error
          const errorKey = setting.id ? 'updateSetting' : 'createSetting'
          errors.value[loadingKey]
            = infraGoogleCustomSearchStore.errors[errorKey]
        }
      } finally {
        loadings.value[loadingKey] = false
      }
    }
  })
}

function onDeleteSetting(id: string, _index: number) {
  confirm.show({
    title: '設定削除の確認',
    description: '設定を削除しますか？この操作は取り消せません。',
    confirmText: '削除',
    onConfirm: async () => {
      const result = await infraGoogleCustomSearchStore.deleteSetting(
        id,
        selectedTenantId.value,
        selectedEnvId.value
      )
      if (result) {
        toast.add({
          id: 'setting-delete',
          title: '設定削除',
          description: '設定を削除しました。',
          color: 'green'
        })
      } else {
        toast.add({
          id: 'setting-delete-error',
          title: 'エラー',
          description: '設定の削除に失敗しました。',
          color: 'red'
        })
      }
    }
  })
}
</script>
