<script setup lang="ts">
// Define required permissions for this page
import SettingsSystemRagAPI from '~/components/settings/system/SettingsSystemRagAPI.vue'

definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_system_settings']
})

const { selectedTenantId, selectedEnvId } = useApp()
const settingsStore = useSettingsStore()
const { featureSettings } = storeToRefs(settingsStore)

// Fetch feature settings on mount to check web search enabled status
onMounted(() => {
  settingsStore.fetchFeatureSettings(
    selectedTenantId.value,
    selectedEnvId.value
  )
})

const allItems = [
  {
    key: 'basic-rag-api',
    label: 'Basic RAG API',
    icon: 'material-symbols:api'
  },
  {
    key: 'azure-ai-search',
    label: 'Azure AI Search',
    icon: 'material-icon-theme:azure'
  },
  {
    key: 'google-custom-search',
    label: 'Google Custom Search',
    icon: 'devicon:google'
  },
  {
    key: 'azure-blob-storage',
    label: 'Azure Blob Storage',
    icon: 'clarity:storage-solid'
  }
]

// Filter items based on web search enabled status
const items = computed(() => {
  const currentFeatureSettings
    = featureSettings.value[selectedTenantId.value]?.[selectedEnvId.value]
  const isWebSearchEnabled = currentFeatureSettings?.is_web_search_enabled

  // If web search is not enabled, exclude Google Custom Search
  if (!isWebSearchEnabled) {
    return allItems.filter(item => item.key !== 'google-custom-search')
  }

  return allItems
})
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <UTabs :items="items">
      <template #item="{ item }">
        <UCard>
          <template #header>
            <p
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              {{ item.label }}
            </p>
          </template>
          <SettingsSystemRagAPI v-if="item.key === 'basic-rag-api'" />
          <SettingsSystemAzureAISearch v-if="item.key === 'azure-ai-search'" />
          <SettingsSystemGoogleCustomSearch
            v-if="item.key === 'google-custom-search'"
          />
          <SettingsSystemAzureBlobStorage
            v-if="item.key === 'azure-blob-storage'"
          />
        </UCard>
      </template>
    </UTabs>
  </UDashboardPanelContent>
</template>
