import { cloneDeep } from 'lodash'

export const useInfraGoogleCustomSearchStore = defineStore('infraGoogleCustomSearchStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    settings: [] as any[],
    originalSettings: [] as any[],
    settingFields: [
      {
        key: 'category',
        label: 'カテゴリ',
        required: true,
        type: 'text'
      },
      {
        key: 'description',
        label: '説明',
        required: false,
        type: 'textarea'
      },
      {
        key: 'api_key',
        label: 'API Key',
        required: true,
        type: 'password'
      },
      {
        key: 'cse_id',
        label: 'CSE ID',
        required: true,
        type: 'text'
      },
      {
        key: 'language',
        label: '言語',
        required: true,
        type: 'select',
        options: [
          { label: '日本語', value: 'lang_ja' },
          { label: '英語', value: 'lang_en' }
        ]
      },
      {
        key: 'safe_search',
        label: 'セーフサーチ',
        required: false,
        type: 'boolean'
      },
      {
        key: 'deep_scrapping',
        label: 'ディープスクラッピング',
        required: false,
        type: 'number',
        min: 0,
        max: 10
      },
      {
        key: 'use_limit',
        label: '使用制限',
        required: false,
        type: 'number',
        min: 0
      },
      {
        key: 'enabled',
        label: '有効',
        required: false,
        type: 'boolean'
      },
      {
        key: 'extra_settings',
        label: '追加設定 (JSON)',
        required: false,
        type: 'json'
      },
      {
        key: 'id',
        label: 'ID',
        readonly: true
      },
      {
        key: 'env_id',
        label: 'Env ID',
        readonly: true
      },
      {
        key: 'tenant_id',
        label: 'テナントID',
        readonly: true
      },
      {
        key: 'created_username',
        label: '作成者',
        readonly: true
      },
      {
        key: 'created_at',
        label: '作成日時',
        readonly: true
      },
      {
        key: 'updated_username',
        label: '更新者',
        readonly: true
      },
      {
        key: 'updated_at',
        label: '更新日時',
        readonly: true
      }
    ] as Record<string, any>[]
  }),
  getters: {
    editableSettingFields(): any[] {
      return this.settingFields.filter((field: any) => !field.readonly)
    },
    readonlySettingFields(): any[] {
      return this.settingFields.filter((field: any) => field.readonly)
    }
  },
  actions: {
    async getAllSettings(tenant_id: string, env_id: string) {
      try {
        this.loadings.getAllSettings = true
        this.errors.getAllSettings = null
        const res = await useAPI().adminService.get(
          `/v2/infra/googleCustomSearch/all/tenants/${tenant_id}/env/${env_id}`
        )
        this.settings = (res.data.settings || []).map((setting: any) => {
          setting.extra_settings = this.formatExtraSettingsForDisplay(setting.extra_settings)
          return setting
        })
        this.originalSettings = cloneDeep(res.data.settings || [])
        return res.data.settings
      } catch (error: any) {
        this.errors.getAllSettings = error?.response?.data || error
        this.settings = []
        this.originalSettings = []
        return false
      } finally {
        this.loadings.getAllSettings = false
      }
    },

    async createSetting(
      tenant_id: string,
      env_id: string,
      payload: any
    ) {
      try {
        this.loadings.createSetting = true
        this.errors.createSetting = null

        // Process extra_settings
        const processedPayload = { ...payload }
        if (processedPayload.extra_settings && typeof processedPayload.extra_settings === 'string') {
          try {
            processedPayload.extra_settings = JSON.parse(processedPayload.extra_settings)
          } catch {
            processedPayload.extra_settings = {}
          }
        }

        const response = await useAPI().adminService.post(
          `/v2/infra/googleCustomSearch/tenants/${tenant_id}/env/${env_id}`,
          processedPayload
        )
        // Add the new setting to the list
        this.settings.push(response.data)
        this.originalSettings = cloneDeep(this.settings)
        return response.data
      } catch (error: any) {
        this.errors.createSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.createSetting = false
      }
    },

    async updateSetting(
      id: string,
      tenant_id: string,
      env_id: string,
      payload: any
    ) {
      try {
        this.loadings.updateSetting = true
        this.errors.updateSetting = null

        // Process extra_settings
        const processedPayload = { ...payload }
        if (processedPayload.extra_settings && typeof processedPayload.extra_settings === 'string') {
          try {
            processedPayload.extra_settings = JSON.parse(processedPayload.extra_settings)
          } catch {
            processedPayload.extra_settings = {}
          }
        }

        const response = await useAPI().adminService.put(
          `/v2/infra/googleCustomSearch/${id}/tenants/${tenant_id}/env/${env_id}`,
          processedPayload
        )
        // Update the setting in the list
        const index = this.settings.findIndex(s => s.id === id)
        if (index !== -1) {
          this.settings[index] = response.data
          this.originalSettings = cloneDeep(this.settings)
        }
        return response.data
      } catch (error: any) {
        this.errors.updateSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateSetting = false
      }
    },

    async deleteSetting(
      id: string,
      tenant_id: string,
      env_id: string
    ) {
      try {
        this.loadings.deleteSetting = true
        this.errors.deleteSetting = null
        await useAPI().adminService.delete(
          `/v2/infra/googleCustomSearch/${id}/tenants/${tenant_id}/env/${env_id}`
        )
        // Remove the setting from the list
        this.settings = this.settings.filter(s => s.id !== id)
        this.originalSettings = cloneDeep(this.settings)
        return true
      } catch (error: any) {
        this.errors.deleteSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteSetting = false
      }
    },

    createNewSetting() {
      return {
        category: '',
        description: '',
        api_key: '',
        cse_id: '',
        language: 'lang_ja',
        safe_search: true,
        deep_scrapping: 0,
        use_limit: 0,
        enabled: true,
        extra_settings: '{}',
        isEditing: true
      }
    },

    // Helper method to format extra_settings for display
    formatExtraSettingsForDisplay(settings: any) {
      if (typeof settings === 'object' && settings !== null) {
        return JSON.stringify(settings, null, 2)
      }
      return settings || '{}'
    },

    // Helper method to prepare settings for editing
    prepareSettingForEdit(setting: any) {
      const editableSetting = { ...setting }
      if (editableSetting.extra_settings && typeof editableSetting.extra_settings === 'object') {
        editableSetting.extra_settings = JSON.stringify(editableSetting.extra_settings, null, 2)
      }
      return editableSetting
    },

    resetErrors() {
      this.errors = {}
    }
  }
})
